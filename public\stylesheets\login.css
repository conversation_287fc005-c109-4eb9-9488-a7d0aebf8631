body {
  margin: 0;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f5f7fa;
  color: #333;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

header {
  background-color: #1e1e2f;
  color: white;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

header h1 {
  margin: 0;
  font-size: 2rem;
}

.back-btn {
  background-color: #333;
  color: white;
  padding: 8px 15px;
  border-radius: 5px;
  text-decoration: none;
  font-size: 0.9rem;
  transition: background-color 0.3s;
}

.back-btn:hover {
  background-color: #555;
}

main {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.alert {
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 5px;
  font-weight: bold;
  text-align: center;
  width: 100%;
  max-width: 400px;
}

.alert p {
  margin: 5px 0;
}

.alert.error {
  background-color: #ffe0e0;
  color: #cc0000;
  border-left: 4px solid #cc0000;
}

.alert.success {
  background-color: #e0ffe0;
  color: #006600;
  border-left: 4px solid #006600;
}

form {
  width: 100%;
  max-width: 400px;
  background: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  gap: 15px;
}

h4 {
  color: #1e1e2f;
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 1.5rem;
  text-align: center;
}

input {
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 1rem;
  transition: border-color 0.3s;
}

input:focus {
  border-color: #1e1e2f;
  outline: none;
  box-shadow: 0 0 0 2px rgba(30, 30, 47, 0.2);
}

input[type="submit"] {
  background-color: #1e1e2f;
  color: white;
  font-weight: bold;
  cursor: pointer;
  padding: 12px;
  border: none;
  margin-top: 10px;
  transition: background-color 0.3s;
}

input[type="submit"]:hover {
  background-color: #2d2d45;
}
