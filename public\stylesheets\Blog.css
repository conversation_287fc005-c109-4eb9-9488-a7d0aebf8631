body {
  margin: 0;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f9f9f9;
  color: #333;
}

header {
  background-color: #1e1e2f;
  color: white;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.logo {
  font-size: 1.5rem;
  text-decoration: none;
  color: white;
  font-weight: bold;
}

nav.category {
  display: flex;
  gap: 1rem;
}

nav.category a {
  color: #ffcc00;
  text-decoration: none;
  font-weight: bold;
  transition: color 0.3s;
}

nav.category a:hover {
  color: #ffffff;
}

.alert {
  margin: 20px auto;
  padding: 15px;
  max-width: 800px;
  border-radius: 5px;
  font-weight: bold;
  text-align: center;
}

.alert.error {
  background-color: #ffe0e0;
  color: #cc0000;
}

.alert.success {
  background-color: #e0ffe0;
  color: #006600;
}

.advert {
  background: linear-gradient(135deg, #eaeaea 0%, #f5f5f5 100%);
  text-align: center;
  padding: 30px;
  margin: 20px auto;
  font-size: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

main.blog-detail {
  max-width: 800px;
  margin: 30px auto;
  padding: 30px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.blog-detail .title {
  font-size: 2.2rem;
  color: #1e1e2f;
  margin-bottom: 20px;
  line-height: 1.3;
}

/* Updated image styling for equal sizes */
.blog-detail .images img {
  width: 100%;
  height: 300px;
  margin: 10px 0;
  border-radius: 8px;
  object-fit: cover;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.blog-detail .content {
  font-size: 1.1rem;
  line-height: 1.8;
  margin-top: 25px;
  color: #444;
}

section.preview {
  max-width: 1000px;
  margin: 40px auto;
  padding: 20px;
}

.preview h2 {
  margin-bottom: 25px;
  text-align: center;
  color: #1e1e2f;
  font-size: 1.8rem;
  position: relative;
}

.preview h2:after {
  content: "";
  display: block;
  width: 80px;
  height: 3px;
  background: #1e1e2f;
  margin: 15px auto 0;
}

.blog-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 25px;
}

.blog-card {
  background: white;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  padding: 25px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-top: 4px solid #1e1e2f;
}

.blog-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
}

.blog-card h3 {
  margin-top: 0;
  color: #1e1e2f;
  font-size: 1.3rem;
}

.blog-card .date {
  color: #666;
  font-size: 0.9rem;
  margin-top: 8px;
  display: block;
}

footer {
  background-color: #1e1e2f;
  color: white;
  text-align: center;
  padding: 30px;
  margin-top: 60px;
}

footer p {
  margin: 0;
  font-size: 1.1rem;
}

/* Updated row images styling */
.images.row {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin-top: 20px;
}

.images.row img {
  width: 100%;
  height: 200px;
  border-radius: 8px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.images.row img:hover {
  transform: scale(1.03);
}

@media (max-width: 768px) {
  .images.row {
    grid-template-columns: repeat(2, 1fr);
  }

  .blog-detail .title {
    font-size: 1.8rem;
  }
}

@media (max-width: 480px) {
  .images.row {
    grid-template-columns: 1fr;
  }

  .blog-detail .images img {
    height: 250px;
  }
}
