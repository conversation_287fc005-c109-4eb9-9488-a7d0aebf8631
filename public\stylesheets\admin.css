/* Admin dashboard styling */
body {
  margin: 0;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f5f7fa;
  color: #333;
}

header {
  background-color: #1e1e2f;
  color: white;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

header h1,
header h2 {
  margin: 0;
}

main {
  max-width: 1000px;
  margin: 30px auto;
  padding: 20px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.alert {
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 5px;
  font-weight: bold;
  text-align: center;
}

.alert.error {
  background-color: #ffe0e0;
  color: #cc0000;
  border-left: 4px solid #cc0000;
}

.alert.success {
  background-color: #e0ffe0;
  color: #006600;
  border-left: 4px solid #006600;
}

/* Updated admin actions styling */
.admin-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 25px;
  margin-top: 30px;
}

.admin-btn {
  display: block;
  padding: 20px;
  background: linear-gradient(135deg, #1e1e2f 0%, #2d2d45 100%);
  color: white;
  text-decoration: none;
  border-radius: 8px;
  font-weight: bold;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  z-index: 1;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  font-size: 1.1rem;
}

.admin-btn:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #2d2d45 0%, #1e1e2f 100%);
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease;
}

.admin-btn:hover:before {
  opacity: 1;
}

.admin-btn:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}
