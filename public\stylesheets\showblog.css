body {
  margin: 0;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f5f7fa;
  color: #333;
}

header {
  background-color: #1e1e2f;
  color: white;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

header h1 {
  margin: 0;
}

.back-btn {
  background-color: #333;
  color: white;
  padding: 8px 15px;
  border-radius: 5px;
  text-decoration: none;
  font-size: 0.9rem;
  transition: background-color 0.3s;
}

.back-btn:hover {
  background-color: #555;
}

.alert {
  margin: 20px auto;
  padding: 15px;
  max-width: 800px;
  border-radius: 5px;
  font-weight: bold;
  text-align: center;
}

.alert p {
  margin: 5px 0;
}

.alert.error {
  background-color: #ffe0e0;
  color: #cc0000;
  border-left: 4px solid #cc0000;
}

.alert.success {
  background-color: #e0ffe0;
  color: #006600;
  border-left: 4px solid #006600;
}

main {
  max-width: 1000px;
  margin: 30px auto;
  padding: 20px;
}

main > div {
  background: white;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

main > div > div {
  padding: 10px;
}

main > div > div:nth-child(4) {
  grid-column: 1 / -1;
  border-top: 1px solid #eee;
  padding-top: 15px;
}

h3 {
  color: #1e1e2f;
  margin-top: 0;
  margin-bottom: 5px;
  font-size: 1rem;
}

p {
  margin: 0;
  font-size: 1.1rem;
}

.clicks {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.clicks a {
  padding: 8px 15px;
  background-color: #1e1e2f;
  color: white;
  text-decoration: none;
  border-radius: 5px;
  font-size: 0.9rem;
  transition: background-color 0.3s;
}

.clicks a:hover {
  background-color: #2d2d45;
}

.clicks a:last-child {
  background-color: #cc0000;
}

.clicks a:last-child:hover {
  background-color: #aa0000;
}

@media (max-width: 768px) {
  header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  main > div {
    grid-template-columns: 1fr;
    gap: 10px;
  }
}
