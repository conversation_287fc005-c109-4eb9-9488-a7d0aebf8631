body {
  margin: 0;
  padding: 0;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
  color: #333;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.error-container {
  text-align: center;
  background-color: white;
  padding: 50px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 90%;
  position: relative;
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.error-code {
  font-size: 8rem;
  font-weight: bold;
  color: #1e1e2f;
  line-height: 1;
  margin-bottom: 10px;
  text-shadow: 2px 2px 0px rgba(0, 0, 0, 0.1);
  position: relative;
}

.error-code:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, transparent, #1e1e2f, transparent);
}

.error-message {
  font-size: 2rem;
  font-weight: bold;
  color: #1e1e2f;
  margin-bottom: 20px;
}

.error-description {
  color: #666;
  margin-bottom: 30px;
  line-height: 1.6;
}

.error-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.btn-primary, .btn-secondary {
  padding: 12px 25px;
  border-radius: 50px;
  font-weight: bold;
  text-decoration: none;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(135deg, #1e1e2f 0%, #2d2d45 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(30, 30, 47, 0.3);
}

.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(30, 30, 47, 0.4);
}

.btn-secondary {
  background-color: transparent;
  color: #1e1e2f;
  border: 2px solid #1e1e2f;
}

.btn-secondary:hover {
  background-color: rgba(30, 30, 47, 0.05);
}

@media (max-width: 480px) {
  .error-code {
    font-size: 6rem;
  }
  
  .error-message {
    font-size: 1.5rem;
  }
  
  .error-actions {
    flex-direction: column;
    gap: 10px;
  }
  
  .btn-primary, .btn-secondary {
    width: 100%;
    text-align: center;
  }
}