/* Specific styles for the create blog page */
header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
}

.back-btn {
  background-color: #333;
  color: white;
  padding: 8px 15px;
  border-radius: 5px;
  text-decoration: none;
  font-size: 0.9rem;
  transition: background-color 0.3s;
}

.back-btn:hover {
  background-color: #555;
}

.blog-form {
  max-width: 800px;
  margin: 0 auto;
}

.form-group {
  margin-bottom: 25px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #1e1e2f;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 1rem;
  transition: border-color 0.3s, box-shadow 0.3s;
}

.form-group input:focus,
.form-group textarea:focus {
  border-color: #1e1e2f;
  outline: none;
  box-shadow: 0 0 0 2px rgba(30, 30, 47, 0.2);
}

.chat-input {
  min-height: 200px;
  resize: vertical;
}

.form-group small {
  display: block;
  margin-top: 5px;
  color: #666;
  font-size: 0.85rem;
}

.form-actions {
  display: flex;
  gap: 15px;
  margin-top: 30px;
}

.submit-btn {
  background: linear-gradient(135deg, #1e1e2f 0%, #2d2d45 100%);
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: 5px;
  cursor: pointer;
  font-weight: bold;
  font-size: 1rem;
  transition: all 0.3s;
  flex: 1;
}

.submit-btn:hover {
  background: linear-gradient(135deg, #2d2d45 0%, #1e1e2f 100%);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
  padding: 12px 25px;
  border-radius: 5px;
  text-decoration: none;
  text-align: center;
  font-weight: bold;
  transition: all 0.3s;
}

.cancel-btn:hover {
  background-color: #eaeaea;
}

@media (max-width: 768px) {
  header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .form-actions {
    flex-direction: column;
  }
}