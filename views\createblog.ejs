<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Create Blog Post</title>
    <link rel="stylesheet" href="/stylesheets/admin.css" />
    <link rel="stylesheet" href="/stylesheets/createblog.css" />
  </head>
  <body>
    <header>
      <h1>Create New Blog Post</h1>
      <a href="/admin/index" class="back-btn">Back to Dashboard</a>
    </header>

    <main>
      <% if(hasErr){ %>
      <div class="alert error">
        <% errMsg.forEach(message =>{ %>
        <p><%= message %></p>
        <% }) %>
      </div>
      <% } %> <% if(hasSuccess){ %>
      <div class="alert success">
        <% successMsg.forEach(message =>{ %>
        <p><%= message %></p>
        <% }) %>
      </div>
      <% } %>

      <form
        action="/admin/createblog"
        method="post"
        enctype="multipart/form-data"
        class="blog-form"
      >
        <div class="form-group">
          <label for="title">Blog Title</label>
          <input type="text" id="title" name="title" required />
        </div>

        <div class="form-group">
          <label for="content">Blog Content</label>
          <textarea
            id="content"
            name="content"
            class="chat-input"
            required
          ></textarea>
        </div>

        <div class="form-group">
          <label for="catigory">Category</label>
          <input type="text" id="catigory" name="catigory" required />
        </div>

        <div class="form-group">
          <label for="images">Upload Images (Max 10):</label>
          <input
            type="file"
            name="images"
            id="images"
            multiple
            accept="image/jpeg, image/png, image/gif"
          />
          <small>Allowed formats: JPEG, JPG, PNG, GIF (Max 4MB each)</small>
        </div>

        <div class="form-actions">
          <button type="submit" class="submit-btn">Create Blog Post</button>
          <a href="/admin/index" class="cancel-btn">Cancel</a>
        </div>
      </form>
    </main>
  </body>
</html>
