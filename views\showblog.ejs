<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>99 Gist - Manage Blogs</title>
    <link rel="stylesheet" href="/stylesheets/showblog.css" />
  </head>
  <body>
    <header>
      <h1>Manage Blog Posts</h1>
      <a href="/admin/index" class="back-btn">Back to Dashboard</a>
    </header>

    <% if(hasErr){ %>
    <div class="alert error">
      <% errMsg.forEach(message =>{ %>
      <p><%= message %></p>
      <% }) %>
    </div>
    <% } %> <% if(hasSuccess){ %>
    <div class="alert success">
      <% successMsg.forEach(message =>{ %>
      <p><%= message %></p>
      <% }) %>
    </div>
    <% } %>

    <main>
      <% if(Blogs){ %> <% Blogs.forEach(blog =>{ %>
      <div>
        <div>
          <h3>Title:</h3>
          <p><%= blog.title %></p>
        </div>
        <div>
          <h3>Category:</h3>
          <p><%= blog.catigory %></p>
        </div>
        <div>
          <h3>Date:</h3>
          <p><%= blog.date %></p>
        </div>
        <div class="clicks">
          <a href="/admin/blog/<%= blog._id %>">View</a>
          <a href="/admin/deleteblog/<%= blog._id %>">Delete</a>
        </div>
      </div>
      <% }) %> <% } %>
    </main>
  </body>
</html>
